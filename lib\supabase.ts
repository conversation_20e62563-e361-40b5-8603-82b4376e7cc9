import { createClient } from "@supabase/supabase-js"

const supabaseUrl = "https://yytkxbrofmvizianbgqy.supabase.co"
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl5dGt4YnJvZm12aXppYW5iZ3F5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNDE4NTIsImV4cCI6MjA2NTcxNzg1Mn0.BKugrHBXcViOWVyPhkZkf_r5HCHIADMW4Dvpcik--VI"

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Add a function to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return supabaseUrl && supabaseAnonKey
}

export type InternshipFormData = {
  id?: string;
  created_at?: string;
  photo_url?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  employee_code?: string;
  father_husband_name?: string;
  department?: string;
  company_name?: string;
  date_of_joining?: string;
  place_location?: string;
  date_of_birth?: string;
  present_address?: string;
  permanent_address?: string;
  phone_residence?: string;
  phone_mobile: string;
  marital_status?: string;
  nationality?: string;
  blood_group?: string;
  personal_email: string;
  uan?: string;
  last_pf_no?: string;
  emergency_contact_name?: string;
  emergency_contact_address?: string;
  emergency_contact_relationship?: string;
  emergency_contact_phone?: string;
  nominee_name?: string;
  nominee_dob?: string;
  nominee_mobile?: string;
  nominee_relationship?: string;
  languages_known?: any;
  family_dependants?: any;
  academic_qualifications?: any;
  professional_qualifications?: any;
  extra_curricular?: string;
  reading_habits?: string;
  is_fresher: boolean;
  work_experience?: any;
  hr_rep_name?: string;
  hr_rep_designation?: string;
  hr_company_address?: string;
  hr_contact_no?: string;
  hr_office_email?: string;
  signature_url?: string;
  declaration_date?: string;
  status?: string;
  agreement_accepted: boolean;
  sections_completed?: string[];
  "references"?: any;
  aadhar_url?: string;
  pan_url?: string;
  ssc_marksheet_url?: string;
  ssc_passing_url?: string;
  hsc_marksheet_url?: string;
  hsc_passing_url?: string;
  graduation_marksheet_url?: string;
  graduation_passing_url?: string;
  postgrad_marksheet_url?: string;
  postgrad_passing_url?: string;
  salary_slips_urls?: string;
  increment_letter_url?: string;
  offer_letter_url?: string;
  relieving_letter_url?: string;
  emergency_contact_1_name?: string;
  emergency_contact_1_address?: string;
  emergency_contact_1_relationship?: string;
  emergency_contact_1_phone?: string;
  emergency_contact_2_name?: string;
  emergency_contact_2_address?: string;
  emergency_contact_2_relationship?: string;
  emergency_contact_2_phone?: string;
  pdf_url?: string;
};
