<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Section Layout Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h2 class="text-2xl font-bold mb-6">Family Section Layout Test</h2>
        
        <!-- Test the new grid layout -->
        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl border border-gray-200 dark:border-gray-600">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-4">
                <div class="lg:col-span-3">
                    <div class="relative">
                        <input type="text" class="peer w-full px-4 text-base bg-white border border-gray-300 rounded-xl pt-6 pb-2 min-w-[140px]" placeholder="Name" />
                        <label class="absolute left-4 top-0 -translate-y-1/2 text-xs font-medium text-blue-600 bg-white px-1 rounded z-10">Name</label>
                    </div>
                </div>
                <div class="lg:col-span-2">
                    <div class="relative">
                        <input type="text" class="peer w-full px-4 text-base bg-white border border-gray-300 rounded-xl pt-6 pb-2 min-w-[140px]" placeholder="Relationship" />
                        <label class="absolute left-4 top-0 -translate-y-1/2 text-xs font-medium text-blue-600 bg-white px-1 rounded z-10">Relationship</label>
                    </div>
                </div>
                <div class="lg:col-span-2">
                    <div class="relative">
                        <input type="date" class="peer w-full px-4 text-base bg-white border border-gray-300 rounded-xl pt-6 pb-2 min-w-[140px]" />
                        <label class="absolute left-4 top-0 -translate-y-1/2 text-xs font-medium text-blue-600 bg-white px-1 rounded z-10">Date of Birth</label>
                    </div>
                </div>
                <div class="lg:col-span-2">
                    <div class="relative">
                        <input type="text" class="peer w-full px-4 text-base bg-white border border-gray-300 rounded-xl pt-6 pb-2 min-w-[140px]" placeholder="Mobile" />
                        <label class="absolute left-4 top-0 -translate-y-1/2 text-xs font-medium text-blue-600 bg-white px-1 rounded z-10">Mobile</label>
                    </div>
                </div>
                <div class="lg:col-span-3">
                    <div class="relative">
                        <input type="text" class="peer w-full px-4 text-base bg-white border border-gray-300 rounded-xl pt-6 pb-2 min-w-[140px]" placeholder="Occupation" />
                        <label class="absolute left-4 top-0 -translate-y-1/2 text-xs font-medium text-blue-600 bg-white px-1 rounded z-10">Occupation</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-8 p-4 bg-green-100 rounded-lg">
            <h3 class="font-semibold text-green-800">Changes Made:</h3>
            <ul class="list-disc list-inside text-green-700 mt-2">
                <li>Changed grid from 5 equal columns to 12-column system</li>
                <li>Name: 3 columns (25%)</li>
                <li>Relationship: 2 columns (16.67%)</li>
                <li>Date of Birth: 2 columns (16.67%) - More space for date input</li>
                <li>Mobile: 2 columns (16.67%)</li>
                <li>Occupation: 3 columns (25%)</li>
                <li>Added min-width for date inputs to prevent text cutoff</li>
            </ul>
        </div>
    </div>
</body>
</html>
