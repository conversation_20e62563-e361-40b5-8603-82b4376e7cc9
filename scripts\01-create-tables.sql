-- This schema is updated to reflect the user's provided live database structure.
CREATE TABLE IF NOT EXISTS public.internship_forms (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at timestamp with time zone DEFAULT now(),
  photo_url text,
  first_name character varying,
  middle_name character varying,
  last_name character varying,
  employee_code character varying,
  father_husband_name character varying,
  department character varying,
  company_name character varying DEFAULT 'ABANS Group'::character varying,
  date_of_joining date,
  place_location character varying,
  date_of_birth date,
  present_address text,
  permanent_address text,
  phone_residence character varying,
  phone_mobile character varying,
  marital_status character varying,
  nationality character varying,
  blood_group character varying,
  personal_email character varying,
  uan character varying,
  last_pf_no character varying,
  emergency_contact_name character varying,
  emergency_contact_address text,
  emergency_contact_relationship character varying,
  emergency_contact_phone character varying,
  nominee_name character varying,
  nominee_dob date,
  nominee_mobile character varying,
  nominee_relationship character varying,
  languages_known jsonb DEFAULT '[]'::jsonb,
  family_dependants jsonb DEFAULT '[]'::jsonb,
  academic_qualifications jsonb DEFAULT '[]'::jsonb,
  professional_qualifications jsonb DEFAULT '[]'::jsonb,
  extra_curricular text,
  reading_habits text,
  is_fresher boolean DEFAULT false,
  work_experience jsonb DEFAULT '[]'::jsonb,
  hr_rep_name character varying,
  hr_rep_designation character varying,
  hr_company_address text,
  hr_contact_no character varying,
  hr_office_email character varying,
  signature_url text,
  declaration_date date DEFAULT CURRENT_DATE,
  status character varying DEFAULT 'submitted'::character varying,
  agreement_accepted boolean DEFAULT false,
  sections_completed TEXT[],
  "references" jsonb DEFAULT '[]'::jsonb,
  aadhar_url text,
  pan_url text,
  ssc_marksheet_url text,
  ssc_passing_url text,
  hsc_marksheet_url text,
  hsc_passing_url text,
  graduation_marksheet_url text,
  graduation_passing_url text,
  postgrad_marksheet_url text,
  postgrad_passing_url text,
  salary_slips_urls text,
  increment_letter_url text,
  offer_letter_url text,
  relieving_letter_url text,
  emergency_contact_1_name text,
  emergency_contact_1_address text,
  emergency_contact_1_relationship text,
  emergency_contact_1_phone text,
  emergency_contact_2_name text,
  emergency_contact_2_address text,
  emergency_contact_2_relationship text,
  emergency_contact_2_phone text,
  pdf_url text
);

-- Create storage bucket for file uploads
INSERT INTO storage.buckets (id, name, public) 
VALUES ('form-uploads', 'form-uploads', true)
ON CONFLICT (id) DO NOTHING;

-- Set up Row Level Security (RLS)
ALTER TABLE internship_forms ENABLE ROW LEVEL SECURITY;

-- Allow inserts for authenticated users
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON internship_forms;
CREATE POLICY "Allow insert for authenticated users" ON internship_forms
FOR INSERT WITH CHECK (true);

-- Allow select for authenticated users
DROP POLICY IF EXISTS "Allow select for authenticated users" ON internship_forms;
CREATE POLICY "Allow select for authenticated users" ON internship_forms
FOR SELECT USING (true);

-- Set up storage policies
-- The policy below allows public uploads. You will need to update this in your Supabase project.
-- In the Supabase dashboard, go to the SQL Editor.
-- You may need to first delete the old policy ("Allow upload for authenticated users") from the storage.objects table.
-- Then, run the following command to create a new, permissive policy.
DROP POLICY IF EXISTS "Allow public uploads" ON storage.objects;
CREATE POLICY "Allow public uploads" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'form-uploads');

DROP POLICY IF EXISTS "Allow public access to uploads" ON storage.objects;
CREATE POLICY "Allow public access to uploads" ON storage.objects
FOR SELECT USING (bucket_id = 'form-uploads');
